<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pillar 1: The Body</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: auto;
            height: 350px;
            max-height: 400px;
        }
    </style>
</head>
<body class="bg-[#F3F4F6]">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-10">
            <div class="inline-block bg-[#FF5733] text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">PILLAR 1: THE BODY</div>
            <h1 class="text-4xl md:text-5xl font-black text-[#2c3e50] tracking-tight">Forging a Capable Machine</h1>
            <p class="text-lg text-gray-600 mt-2">Building functional strength and endurance for real-world work.</p>
        </header>

        <section id="workout-split" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-6 text-[#2c3e50]">The Weekly Workout Split</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-center">
                <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-[#FF5733]">
                    <h3 class="font-bold text-lg">Strength Focus</h3>
                    <p class="font-semibold text-[#FF5733]">Mon / Wed / Fri</p>
                    <p class="mt-2 text-sm">Full-body compound exercises to build a powerful foundation. The core of your physical transformation.</p>
                </div>
                <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-[#3357FF]">
                    <h3 class="font-bold text-lg">Cardio & Core</h3>
                    <p class="font-semibold text-[#3357FF]">Tue / Thu</p>
                    <p class="mt-2 text-sm">High-Intensity Interval Training and core work to build work capacity and a resilient midsection.</p>
                </div>
            </div>
        </section>

        <section id="key-lifts" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
             <h2 class="text-2xl font-bold text-center mb-6 text-[#2c3e50]">The Five Foundational Lifts</h2>
             <p class="text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto">Mastering these five compound movements is the fastest path to functional strength. They work multiple muscle groups simultaneously, mimicking real-world physical tasks.</p>
            <div class="chart-container">
                <canvas id="keyLiftsChart"></canvas>
            </div>
        </section>

        <section id="nutrition" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
             <h2 class="text-2xl font-bold text-center mb-6 text-[#2c3e50]">The Fuel Pyramid</h2>
             <p class="text-sm text-gray-600 mb-6 text-center">Your body is a machine; give it the right fuel. Follow this simple hierarchy for optimal performance and recovery. No complex diet needed.</p>
            <div class="relative max-w-sm mx-auto text-center font-bold text-white">
                <div class="w-full h-24 bg-[#FF5733] flex items-center justify-center clip-pyramid-top rounded-t-lg">PROTEIN (Every Meal)</div>
                <div class="w-full h-20 bg-[#F9A825] flex items-center justify-center clip-pyramid-mid1">COMPLEX CARBS</div>
                <div class="w-full h-16 bg-[#33FF57] flex items-center justify-center clip-pyramid-mid2">VEGETABLES</div>
                <div class="w-full h-12 bg-[#3357FF] flex items-center justify-center clip-pyramid-base rounded-b-lg">3L+ WATER DAILY</div>
            </div>
        </section>
        
        <footer class="text-center mt-10 pt-6 border-t border-gray-300">
            <p class="text-gray-700 font-semibold">Your body is your primary tool. Keep it sharp.</p>
        </footer>

    </div>
    <style>
        .clip-pyramid-top { clip-path: polygon(15% 0, 85% 0, 100% 100%, 0% 100%); }
        .clip-pyramid-mid1 { clip-path: polygon(0 0, 100% 0, 85% 100%, 15% 100%); }
        .clip-pyramid-mid2 { clip-path: polygon(15% 0, 85% 0, 70% 100%, 30% 100%); }
        .clip-pyramid-base { clip-path: polygon(30% 0, 70% 0, 55% 100%, 45% 100%); }
    </style>
    <script>
        const wrapLabel = (label) => {
            const maxLength = 16;
            if (label.length <= maxLength) return label;
            const words = label.split(' ');
            let lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + ' ' + word).trim().length > maxLength) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine = (currentLine + ' ' + word).trim();
                }
            });
            lines.push(currentLine);
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };

        const keyLiftsCtx = document.getElementById('keyLiftsChart').getContext('2d');
        new Chart(keyLiftsCtx, {
            type: 'bar',
            data: {
                labels: ['Squat', 'Deadlift', 'Bench Press', 'Overhead Press', 'Barbell Row'],
                datasets: [{
                    label: 'Functional Strength Rating',
                    data: [95, 100, 85, 80, 90],
                    backgroundColor: ['#FF5733', '#C70039', '#900C3F', '#581845', '#3357FF'],
                    borderColor: '#fff',
                    borderWidth: 2,
                    borderRadius: 5
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { display: false },
                    y: { ticks: { font: { size: 14, weight: 'bold' } } }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                         callbacks: {
                            title: tooltipTitleCallback,
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.x !== null) {
                                    const lift = context.label;
                                    let muscles;
                                    switch(lift) {
                                        case 'Squat': muscles = 'Legs, Glutes, Core'; break;
                                        case 'Deadlift': muscles = 'Entire Posterior Chain'; break;
                                        case 'Bench Press': muscles = 'Chest, Shoulders, Triceps'; break;
                                        case 'Overhead Press': muscles = 'Shoulders, Triceps, Core'; break;
                                        case 'Barbell Row': muscles = 'Back, Biceps'; break;
                                        default: muscles = '';
                                    }
                                    return ` Works: ${muscles}`;
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
