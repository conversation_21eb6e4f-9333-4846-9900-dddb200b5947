<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pillar 4: The Mind</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f7f9fc; }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 380px;
            margin: auto;
            height: 350px;
            max-height: 400px;
        }
    </style>
</head>
<body class="bg-[#f7f9fc]">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-10">
            <div class="inline-block bg-[#4A90E2] text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">PILLAR 4: THE MIND</div>
            <h1 class="text-4xl md:text-5xl font-black text-[#34495e] tracking-tight">Sharpening Your Greatest Tool</h1>
            <p class="text-lg text-gray-600 mt-2">Rewire your brain for deep focus and accelerated learning.</p>
        </header>

        <section id="core-practices" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-6 text-[#34495e]">The Three Core Practices</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div class="p-4">
                    <div class="text-6xl">🧘‍♂️</div>
                    <h3 class="font-bold text-lg mt-3 text-[#4A90E2]">Meditation</h3>
                    <p class="text-sm mt-1">Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.</p>
                </div>
                <div class="p-4">
                    <div class="text-6xl">📚</div>
                    <h3 class="font-bold text-lg mt-3 text-[#50E3C2]">Active Reading</h3>
                    <p class="text-sm mt-1">Don't just read, process. Use the Feynman Technique to truly understand and retain information.</p>
                </div>
                <div class="p-4">
                    <div class="text-6xl">🧩</div>
                    <h3 class="font-bold text-lg mt-3 text-[#F5A623]">Problem Solving</h3>
                    <p class="text-sm mt-1">Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.</p>
                </div>
            </div>
        </section>

        <section class="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center">
             <div class="md:col-span-3 bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold text-center mb-6 text-[#34495e]">The Feynman Learning Technique</h2>
                <div class="space-y-4">
                    <div class="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-[#4A90E2]">1.</div>
                        <div><strong class="text-gray-800">Choose a Concept.</strong> Pick a topic you are learning (e.g., a grammar point, how a tool works).</div>
                    </div>
                    <div class="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-[#4A90E2]">2.</div>
                        <div><strong class="text-gray-800">Teach it Simply.</strong> Explain it out loud, in the simplest terms possible, as if to a child.</div>
                    </div>
                    <div class="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-[#4A90E2]">3.</div>
                        <div><strong class="text-gray-800">Identify Gaps.</strong> When you get stuck or use complex language, you've found a weak spot in your understanding.</div>
                    </div>
                    <div class="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-[#4A90E2]">4.</div>
                        <div><strong class="text-gray-800">Review & Simplify.</strong> Go back to the source material to fill the gap, then refine your simple explanation.</div>
                    </div>
                </div>
             </div>
             <div class="md:col-span-2 bg-white p-6 rounded-lg shadow-lg">
                 <h2 class="text-2xl font-bold text-center mb-4 text-[#34495e]">Daily Cognitive Workout</h2>
                 <p class="text-sm text-center text-gray-600 mb-4">Your 90-minute daily block dedicated to mental fitness.</p>
                <div class="chart-container">
                    <canvas id="mindWorkoutChart"></canvas>
                </div>
             </div>
        </section>

        <footer class="text-center mt-10 pt-6 border-t border-gray-300">
            <p class="text-gray-700 font-semibold">The quality of your work is determined by the quality of your focus.</p>
        </footer>

    </div>
    <script>
        const wrapLabel = (label) => {
            const maxLength = 16;
            if (label.length <= maxLength) return label;
            const words = label.split(' ');
            let lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + ' ' + word).trim().length > maxLength) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine = (currentLine + ' ' + word).trim();
                }
            });
            lines.push(currentLine);
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };

        const mindWorkoutCtx = document.getElementById('mindWorkoutChart').getContext('2d');
        new Chart(mindWorkoutCtx, {
            type: 'pie',
            data: {
                labels: ['Active Reading (45 min)', 'Problem Solving (30 min)', 'Meditation (15 min)'],
                datasets: [{
                    data: [50, 33, 17],
                    backgroundColor: ['#50E3C2', '#F5A623', '#4A90E2'],
                    borderColor: '#fff',
                    borderWidth: 4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                     tooltip: {
                         callbacks: {
                            title: tooltipTitleCallback
                        }
                    },
                    title: {
                        display: true,
                        text: '90-Minute Focus Block',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    </script>
</body>
</html>
