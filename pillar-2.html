<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pillar 2: The Voice</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f0f7f8; }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin: auto;
            height: 350px;
            max-height: 400px;
        }
    </style>
</head>
<body class="bg-[#f0f7f8]">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-10">
            <div class="inline-block bg-[#003B46] text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">PILLAR 2: THE VOICE</div>
            <h1 class="text-4xl md:text-5xl font-black text-[#003B46] tracking-tight">Building a Bridge to Japan</h1>
            <p class="text-lg text-[#07575B] mt-2">Your structured path to JLPT N4 conversational fluency.</p>
        </header>

        <section id="method" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-6 text-[#003B46]">The Four-Step Language Method</h2>
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 text-center">
                <div class="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
                    <div class="text-4xl">①</div>
                    <h3 class="font-bold mt-2">Kana Mastery</h3>
                    <p class="text-xs">The Alphabet</p>
                </div>
                <div class="text-4xl text-[#66A5AD]">→</div>
                <div class="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
                    <div class="text-4xl">②</div>
                    <h3 class="font-bold mt-2">Grammar Core</h3>
                    <p class="text-xs">The Skeleton</p>
                </div>
                <div class="text-4xl text-[#66A5AD]">→</div>
                <div class="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
                    <div class="text-4xl">③</div>
                    <h3 class="font-bold mt-2">Anki Vocab</h3>
                    <p class="text-xs">The Muscle</p>
                </div>
                <div class="text-4xl text-[#66A5AD]">→</div>
                 <div class="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
                    <div class="text-4xl">④</div>
                    <h3 class="font-bold mt-2">Immersion</h3>
                    <p class="text-xs">The Real World</p>
                </div>
            </div>
        </section>

        <section class="mb-12 grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold text-center mb-4 text-[#003B46]">The Anki Engine</h2>
                <p class="text-sm text-center text-gray-600 mb-4">Anki is your non-negotiable daily habit for vocabulary. This is how you burn thousands of words into your long-term memory.</p>
                <div class="chart-container">
                    <canvas id="ankiChart"></canvas>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-bold text-center mb-4 text-[#003B46]">Monthly Mission Focus</h2>
                <ul class="space-y-4">
                    <li class="p-3 bg-cyan-50 rounded-md border-l-4 border-[#07575B]">
                        <strong class="text-[#07575B]">Month 1: Foundation.</strong> Master Hiragana & Katakana. Complete first half of Genki I. Start daily Anki habit.
                    </li>
                    <li class="p-3 bg-cyan-50 rounded-md border-l-4 border-[#07575B]">
                        <strong class="text-[#07575B]">Month 2: Acceleration.</strong> Finish Genki I. Start Genki II. Aggressively build vocabulary with Anki (aim for 1000+ words).
                    </li>
                    <li class="p-3 bg-cyan-50 rounded-md border-l-4 border-[#07575B]">
                        <strong class="text-[#07575B]">Month 3: Application.</strong> Continue Genki II. Begin daily active listening (podcasts) and speaking practice (HelloTalk).
                    </li>
                    <li class="p-3 bg-cyan-50 rounded-md border-l-4 border-[#07575B]">
                        <strong class="text-[#07575B]">Month 4: Refinement.</strong> Finish Genki II. Focus heavily on conversational practice and reading simple news (NHK Easy).
                    </li>
                </ul>
            </div>
        </section>
        
        <footer class="text-center mt-10 pt-6 border-t border-[#C4DFE6]">
            <p class="text-[#07575B] font-semibold">Language is a skill built daily, not in a day.</p>
        </footer>

    </div>
    <script>
        const wrapLabel = (label) => {
            const maxLength = 16;
            if (label.length <= maxLength) return label;
            const words = label.split(' ');
            let lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + ' ' + word).trim().length > maxLength) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine = (currentLine + ' ' + word).trim();
                }
            });
            lines.push(currentLine);
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };

        const ankiCtx = document.getElementById('ankiChart').getContext('2d');
        new Chart(ankiCtx, {
            type: 'doughnut',
            data: {
                labels: ['Daily Reviews', 'Learn New Words (20/day)', 'Long-Term Memory'],
                datasets: [{
                    data: [50, 25, 25],
                    backgroundColor: ['#003B46', '#07575B', '#66A5AD'],
                    borderColor: '#fff',
                    borderWidth: 4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                         callbacks: {
                            title: tooltipTitleCallback
                        }
                    },
                    title: {
                        display: true,
                        text: 'The Daily SRS Cycle',
                        font: { size: 16, weight: 'bold' }
                    }
                }
            }
        });
    </script>
</body>
</html>
