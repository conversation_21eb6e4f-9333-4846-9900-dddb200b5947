<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Jack of All Trades: Master Plan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px;
            margin-left: auto;
            margin-right: auto;
            height: 400px;
            max-height: 450px;
        }
        .flow-arrow {
            font-size: 2rem;
            color: #66A5AD;
            line-height: 1;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-black text-[#003B46] tracking-tight">PROJECT: JACK OF ALL TRADES</h1>
            <p class="text-lg md:text-xl text-[#07575B] mt-2">Your 16-Week Blueprint for Total Transformation</p>
        </header>

        <section id="missions" class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#003B46]">Your Monthly Missions</h2>
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4">
                    <div class="p-4">
                        <h3 class="text-xl font-bold text-[#07575B]">Month 1</h3>
                        <p class="font-semibold text-gray-700">Foundation & Discipline</p>
                        <p class="text-sm mt-1">Build unbreakable habits. Master Kana. Learn fundamental strength movements.</p>
                    </div>
                    <div class="hidden md:block flow-arrow">→</div>
                    <div class="p-4">
                        <h3 class="text-xl font-bold text-[#07575B]">Month 2</h3>
                        <p class="font-semibold text-gray-700">Acceleration & Vocabulary</p>
                        <p class="text-sm mt-1">Master basic grammar. Aggressively expand vocabulary. Increase workout intensity.</p>
                    </div>
                    <div class="hidden md:block flow-arrow">→</div>
                     <div class="p-4">
                        <h3 class="text-xl font-bold text-[#07575B]">Month 3</h3>
                        <p class="font-semibold text-gray-700">Application & Practicality</p>
                        <p class="text-sm mt-1">Begin speaking practice. Study practical skills. Build physical work capacity.</p>
                    </div>
                    <div class="hidden md:block flow-arrow">→</div>
                    <div class="p-4">
                        <h3 class="text-xl font-bold text-[#07575B]">Month 4</h3>
                        <p class="font-semibold text-gray-700">Refinement & Fluency</p>
                        <p class="text-sm mt-1">Polish conversation skills. Review all practical knowledge. Achieve peak fitness.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="daily-grind" class="mb-12">
            <h2 class="text-3xl font-bold text-center mb-8 text-[#003B46]">The Daily Grind: Weekday Blueprint</h2>
            <div class="space-y-6">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <p class="text-sm text-center text-gray-600 mb-4">This is your core schedule, Monday to Friday. Each block is non-negotiable. It is designed for maximum efficiency in skill acquisition.</p>
                </div>
                <!-- Time Block 1 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">06:00 - 07:30 | 💪 Pillar 1: Physical Fitness</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Full-body strength training (3x/week) or Cardio & Core (2x/week).</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts.</p>
                </div>
                <!-- Time Block 2 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">09:00 - 12:00 | 🗣️ Pillar 2: Japanese Core Study</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Deep, focused study of Japanese grammar and sentence structure.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days.</p>
                </div>
                 <!-- Time Block 3 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">13:00 - 15:00 | 🛠️ Pillar 3: Practical Skills Study</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Theoretical study of tools, safety protocols, materials, and basic trade knowledge.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a "knowledge base" in a notebook.</p>
                </div>
                 <!-- Time Block 4 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">15:00 - 16:30 | 🧠 Pillar 4: Cognitive Fitness</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Active reading of non-fiction, problem-solving puzzles, and meditation.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> To sharpen your focus, improve memory retention, and train your brain to learn efficiently.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles.</p>
                </div>
                 <!-- Time Block 5 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">17:00 - 18:00 | 🗣️ Pillar 2: Japanese Vocabulary Drill</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Spaced Repetition System (SRS) for vocabulary.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> To efficiently memorize thousands of words and burn them into your long-term memory.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Use the Anki app. Do your reviews and learn 20 new words every single day without fail.</p>
                </div>
                 <!-- Time Block 6 -->
                <div class="bg-white p-5 rounded-lg shadow-md">
                    <div class="font-bold text-lg text-[#003B46]">19:30 - 21:00 | 🗣️ Pillar 2: Japanese Immersion</div>
                    <p class="mt-2 text-sm"><strong class="text-[#07575B]">WHAT:</strong> Actively listen to and speak Japanese.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">WHY:</strong> To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.</p>
                    <p class="mt-1 text-sm"><strong class="text-[#07575B]">HOW:</strong> Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers.</p>
                </div>
            </div>
        </section>

        <section id="weekend-plan" class="mb-12">
             <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h2 class="text-2xl font-bold text-center mb-4 text-[#003B46]">Weekly Time Allocation</h2>
                    <p class="text-sm text-gray-600 mb-4 text-center">Your approximate 40+ hours of active development each week, broken down by pillar.</p>
                    <div class="chart-container">
                        <canvas id="timeAllocationChart"></canvas>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                     <h2 class="text-2xl font-bold text-center mb-4 text-[#003B46]">The Weekend Shift</h2>
                     <p class="text-sm text-gray-600 mb-4 text-center">Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues.</p>
                     <ul class="space-y-4">
                        <li class="p-4 bg-cyan-50 rounded-lg">
                            <h3 class="font-bold text-teal-800">Saturday Morning: Review & Consolidate</h3>
                            <p class="text-sm">Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session.</p>
                        </li>
                         <li class="p-4 bg-cyan-50 rounded-lg">
                            <h3 class="font-bold text-teal-800">Saturday Afternoon: Hands-On Project</h3>
                            <p class="text-sm">Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action.</p>
                        </li>
                         <li class="p-4 bg-cyan-50 rounded-lg">
                            <h3 class="font-bold text-teal-800">Sunday Morning: Active Recovery</h3>
                            <p class="text-sm">Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal.</p>
                        </li>
                         <li class="p-4 bg-cyan-50 rounded-lg">
                            <h3 class="font-bold text-teal-800">Sunday Afternoon: Strategize & Recharge</h3>
                            <p class="text-sm">Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead.</p>
                        </li>
                     </ul>
                </div>
             </div>
        </section>


        <footer class="text-center mt-12 pt-8 border-t border-gray-300">
            <p class="text-gray-600">Discipline today is freedom tomorrow. Execute the plan.</p>
            <p class="text-sm text-gray-500 mt-2">© 2025 Project Reboot Master Plan</p>
        </footer>

    </div>

    <script>
        const wrapLabel = (label) => {
            const maxLength = 16;
            if (label.length <= maxLength) return label;
            const words = label.split(' ');
            const lines = [];
            let currentLine = '';
            for (const word of words) {
                if ((currentLine + ' ' + word).trim().length > maxLength) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine += (currentLine ? ' ' : '') + word;
                }
            }
            lines.push(currentLine);
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };
        
        const timeAllocationCtx = document.getElementById('timeAllocationChart').getContext('2d');
        new Chart(timeAllocationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Pillar 2: Japanese Study (Voice)', 'Pillar 1: Physical Fitness (Body)', 'Pillar 3: Practical Skills (Hands)', 'Pillar 4: Cognitive Fitness (Mind)'],
                datasets: [{
                    label: 'Weekly Hours',
                    data: [20, 7.5, 10, 7.5],
                    backgroundColor: [
                        '#003B46',
                        '#07575B',
                        '#66A5AD',
                        '#C4DFE6'
                    ],
                    borderColor: '#FFFFFF',
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: { size: 12 }
                        }
                    },
                    title: {
                        display: true,
                        text: 'Weekly Hour Distribution',
                        font: { size: 16, weight: 'bold' },
                        padding: { top: 10, bottom: 20 }
                    },
                    tooltip: {
                         callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
