<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pillar 3: The Hands</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; background-color: #f5f5f5; }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 450px;
            margin: auto;
            height: 400px;
            max-height: 450px;
        }
    </style>
</head>
<body class="bg-gray-100">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-10">
            <div class="inline-block bg-[#4A4A4A] text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">PILLAR 3: THE HANDS</div>
            <h1 class="text-4xl md:text-5xl font-black text-[#333] tracking-tight">Acquiring Practical Knowledge</h1>
            <p class="text-lg text-gray-600 mt-2">Know the 'What' and 'Why' to quickly master the 'How' on the job.</p>
        </header>

        <section id="knowledge-areas" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-4 text-[#333]">Core Knowledge Categories</h2>
            <p class="text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto">Your study will focus on these five areas. A balanced understanding across all of them will make you a versatile and adaptable worker.</p>
            <div class="chart-container">
                <canvas id="knowledgeChart"></canvas>
            </div>
        </section>

        <section id="learning-loop" class="mb-12 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-6 text-[#333]">The Learning Loop</h2>
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8">
                <div class="text-center">
                    <div class="w-24 h-24 bg-[#D9534F] text-white rounded-full flex items-center justify-center text-4xl mx-auto">①</div>
                    <h3 class="font-bold mt-3">Watch & Learn</h3>
                    <p class="text-sm">Use curated YouTube channels.</p>
                </div>
                <div class="text-4xl font-light text-gray-400">→</div>
                 <div class="text-center">
                    <div class="w-24 h-24 bg-[#F0AD4E] text-white rounded-full flex items-center justify-center text-4xl mx-auto">②</div>
                    <h3 class="font-bold mt-3">Note & Synthesize</h3>
                    <p class="text-sm">Keep a dedicated notebook.</p>
                </div>
                <div class="text-4xl font-light text-gray-400">→</div>
                 <div class="text-center">
                    <div class="w-24 h-24 bg-[#5BC0DE] text-white rounded-full flex items-center justify-center text-4xl mx-auto">③</div>
                    <h3 class="font-bold mt-3">Apply & Practice</h3>
                    <p class="text-sm">Do weekend hands-on projects.</p>
                </div>
            </div>
        </section>

        <section id="safety" class="mb-12 bg-[#F0AD4E] text-gray-800 p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold text-center mb-4">Safety is Non-Negotiable</h2>
            <p class="text-center text-sm mb-6">Understanding and respecting safety protocols is the mark of a professional. Learn the purpose of each piece of Personal Protective Equipment (PPE).</p>
            <div class="flex justify-around text-center">
                <div><div class="text-5xl">👷</div><p class="font-semibold mt-2">Hard Hat</p></div>
                <div><div class="text-5xl">👓</div><p class="font-semibold mt-2">Safety Glasses</p></div>
                <div><div class="text-5xl">🧤</div><p class="font-semibold mt-2">Gloves</p></div>
                <div><div class="text-5xl">🥾</div><p class="font-semibold mt-2">Steel-Toe Boots</p></div>
            </div>
        </section>
        
        <footer class="text-center mt-10 pt-6 border-t border-gray-300">
            <p class="text-gray-700 font-semibold">A smart hand is a safe and productive hand.</p>
        </footer>

    </div>
    <script>
        const wrapLabel = (label) => {
            const maxLength = 16;
            if (label.length <= maxLength) return label;
            const words = label.split(' ');
            let lines = [];
            let currentLine = '';
            words.forEach(word => {
                if ((currentLine + ' ' + word).trim().length > maxLength) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine = (currentLine + ' ' + word).trim();
                }
            });
            lines.push(currentLine);
            return lines;
        };

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
                return label.join(' ');
            } else {
                return label;
            }
        };

        const knowledgeCtx = document.getElementById('knowledgeChart').getContext('2d');
        new Chart(knowledgeCtx, {
            type: 'radar',
            data: {
                labels: ['Hand Tools', 'Power Tools', 'Site Safety', 'Materials ID', ['Essential Skills', '(Knots, First Aid)']],
                datasets: [{
                    label: 'Study Focus',
                    data: [8, 7, 10, 6, 9],
                    backgroundColor: 'rgba(217, 83, 79, 0.2)',
                    borderColor: '#D9534F',
                    pointBackgroundColor: '#D9534F',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#D9534F',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: { color: '#ddd' },
                        grid: { color: '#ddd' },
                        pointLabels: { font: { size: 12, weight: 'bold' } },
                        ticks: { display: false }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                         callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
